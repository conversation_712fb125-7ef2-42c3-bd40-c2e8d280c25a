// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delivery.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OtkDeliveriesResponse _$OtkDeliveriesResponseFromJson(
    Map<String, dynamic> json) {
  return _OtkDeliveriesResponse.fromJson(json);
}

/// @nodoc
mixin _$OtkDeliveriesResponse {
  List<OtkDeliveryModel> get deliveries => throw _privateConstructorUsedError;
  int get total => throw _privateConstructorUsedError;

  /// Serializes this OtkDeliveriesResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkDeliveriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkDeliveriesResponseCopyWith<OtkDeliveriesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkDeliveriesResponseCopyWith<$Res> {
  factory $OtkDeliveriesResponseCopyWith(OtkDeliveriesResponse value,
          $Res Function(OtkDeliveriesResponse) then) =
      _$OtkDeliveriesResponseCopyWithImpl<$Res, OtkDeliveriesResponse>;
  @useResult
  $Res call({List<OtkDeliveryModel> deliveries, int total});
}

/// @nodoc
class _$OtkDeliveriesResponseCopyWithImpl<$Res,
        $Val extends OtkDeliveriesResponse>
    implements $OtkDeliveriesResponseCopyWith<$Res> {
  _$OtkDeliveriesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkDeliveriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deliveries = null,
    Object? total = null,
  }) {
    return _then(_value.copyWith(
      deliveries: null == deliveries
          ? _value.deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<OtkDeliveryModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtkDeliveriesResponseImplCopyWith<$Res>
    implements $OtkDeliveriesResponseCopyWith<$Res> {
  factory _$$OtkDeliveriesResponseImplCopyWith(
          _$OtkDeliveriesResponseImpl value,
          $Res Function(_$OtkDeliveriesResponseImpl) then) =
      __$$OtkDeliveriesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<OtkDeliveryModel> deliveries, int total});
}

/// @nodoc
class __$$OtkDeliveriesResponseImplCopyWithImpl<$Res>
    extends _$OtkDeliveriesResponseCopyWithImpl<$Res,
        _$OtkDeliveriesResponseImpl>
    implements _$$OtkDeliveriesResponseImplCopyWith<$Res> {
  __$$OtkDeliveriesResponseImplCopyWithImpl(_$OtkDeliveriesResponseImpl _value,
      $Res Function(_$OtkDeliveriesResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkDeliveriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deliveries = null,
    Object? total = null,
  }) {
    return _then(_$OtkDeliveriesResponseImpl(
      deliveries: null == deliveries
          ? _value._deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<OtkDeliveryModel>,
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkDeliveriesResponseImpl implements _OtkDeliveriesResponse {
  const _$OtkDeliveriesResponseImpl(
      {final List<OtkDeliveryModel> deliveries = const [], this.total = 0})
      : _deliveries = deliveries;

  factory _$OtkDeliveriesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkDeliveriesResponseImplFromJson(json);

  final List<OtkDeliveryModel> _deliveries;
  @override
  @JsonKey()
  List<OtkDeliveryModel> get deliveries {
    if (_deliveries is EqualUnmodifiableListView) return _deliveries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deliveries);
  }

  @override
  @JsonKey()
  final int total;

  @override
  String toString() {
    return 'OtkDeliveriesResponse(deliveries: $deliveries, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkDeliveriesResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._deliveries, _deliveries) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_deliveries), total);

  /// Create a copy of OtkDeliveriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkDeliveriesResponseImplCopyWith<_$OtkDeliveriesResponseImpl>
      get copyWith => __$$OtkDeliveriesResponseImplCopyWithImpl<
          _$OtkDeliveriesResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkDeliveriesResponseImplToJson(
      this,
    );
  }
}

abstract class _OtkDeliveriesResponse implements OtkDeliveriesResponse {
  const factory _OtkDeliveriesResponse(
      {final List<OtkDeliveryModel> deliveries,
      final int total}) = _$OtkDeliveriesResponseImpl;

  factory _OtkDeliveriesResponse.fromJson(Map<String, dynamic> json) =
      _$OtkDeliveriesResponseImpl.fromJson;

  @override
  List<OtkDeliveryModel> get deliveries;
  @override
  int get total;

  /// Create a copy of OtkDeliveriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkDeliveriesResponseImplCopyWith<_$OtkDeliveriesResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OtkDeliveryModel _$OtkDeliveryModelFromJson(Map<String, dynamic> json) {
  return _OtkDeliveryModel.fromJson(json);
}

/// @nodoc
mixin _$OtkDeliveryModel {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  String? get deliveryId => throw _privateConstructorUsedError;
  String? get warehouseId => throw _privateConstructorUsedError;
  ProjectModel? get project => throw _privateConstructorUsedError;
  DeliveryModel? get delivery => throw _privateConstructorUsedError;
  ClientModel? get supplier => throw _privateConstructorUsedError;
  DateTime? get receivedAt => throw _privateConstructorUsedError;
  String? get receivedBy => throw _privateConstructorUsedError;
  OtkStatus? get status => throw _privateConstructorUsedError;
  List<OtkItemModel>? get items => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  DateTime? get checkedAt => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt =>
      throw _privateConstructorUsedError; // Дополнительные поля из API
  ContractModel? get contract => throw _privateConstructorUsedError;

  /// Serializes this OtkDeliveryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkDeliveryModelCopyWith<OtkDeliveryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkDeliveryModelCopyWith<$Res> {
  factory $OtkDeliveryModelCopyWith(
          OtkDeliveryModel value, $Res Function(OtkDeliveryModel) then) =
      _$OtkDeliveryModelCopyWithImpl<$Res, OtkDeliveryModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      String? deliveryId,
      String? warehouseId,
      ProjectModel? project,
      DeliveryModel? delivery,
      ClientModel? supplier,
      DateTime? receivedAt,
      String? receivedBy,
      OtkStatus? status,
      List<OtkItemModel>? items,
      String? comment,
      DateTime? checkedAt,
      DateTime? createdAt,
      DateTime? updatedAt,
      ContractModel? contract});

  $DeliveryModelCopyWith<$Res>? get delivery;
  $ContractModelCopyWith<$Res>? get contract;
}

/// @nodoc
class _$OtkDeliveryModelCopyWithImpl<$Res, $Val extends OtkDeliveryModel>
    implements $OtkDeliveryModelCopyWith<$Res> {
  _$OtkDeliveryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? deliveryId = freezed,
    Object? warehouseId = freezed,
    Object? project = freezed,
    Object? delivery = freezed,
    Object? supplier = freezed,
    Object? receivedAt = freezed,
    Object? receivedBy = freezed,
    Object? status = freezed,
    Object? items = freezed,
    Object? comment = freezed,
    Object? checkedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? contract = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryId: freezed == deliveryId
          ? _value.deliveryId
          : deliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      warehouseId: freezed == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String?,
      project: freezed == project
          ? _value.project
          : project // ignore: cast_nullable_to_non_nullable
              as ProjectModel?,
      delivery: freezed == delivery
          ? _value.delivery
          : delivery // ignore: cast_nullable_to_non_nullable
              as DeliveryModel?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      receivedAt: freezed == receivedAt
          ? _value.receivedAt
          : receivedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      receivedBy: freezed == receivedBy
          ? _value.receivedBy
          : receivedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as OtkStatus?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<OtkItemModel>?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      checkedAt: freezed == checkedAt
          ? _value.checkedAt
          : checkedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contract: freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as ContractModel?,
    ) as $Val);
  }

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DeliveryModelCopyWith<$Res>? get delivery {
    if (_value.delivery == null) {
      return null;
    }

    return $DeliveryModelCopyWith<$Res>(_value.delivery!, (value) {
      return _then(_value.copyWith(delivery: value) as $Val);
    });
  }

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContractModelCopyWith<$Res>? get contract {
    if (_value.contract == null) {
      return null;
    }

    return $ContractModelCopyWith<$Res>(_value.contract!, (value) {
      return _then(_value.copyWith(contract: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OtkDeliveryModelImplCopyWith<$Res>
    implements $OtkDeliveryModelCopyWith<$Res> {
  factory _$$OtkDeliveryModelImplCopyWith(_$OtkDeliveryModelImpl value,
          $Res Function(_$OtkDeliveryModelImpl) then) =
      __$$OtkDeliveryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      String? deliveryId,
      String? warehouseId,
      ProjectModel? project,
      DeliveryModel? delivery,
      ClientModel? supplier,
      DateTime? receivedAt,
      String? receivedBy,
      OtkStatus? status,
      List<OtkItemModel>? items,
      String? comment,
      DateTime? checkedAt,
      DateTime? createdAt,
      DateTime? updatedAt,
      ContractModel? contract});

  @override
  $DeliveryModelCopyWith<$Res>? get delivery;
  @override
  $ContractModelCopyWith<$Res>? get contract;
}

/// @nodoc
class __$$OtkDeliveryModelImplCopyWithImpl<$Res>
    extends _$OtkDeliveryModelCopyWithImpl<$Res, _$OtkDeliveryModelImpl>
    implements _$$OtkDeliveryModelImplCopyWith<$Res> {
  __$$OtkDeliveryModelImplCopyWithImpl(_$OtkDeliveryModelImpl _value,
      $Res Function(_$OtkDeliveryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? deliveryId = freezed,
    Object? warehouseId = freezed,
    Object? project = freezed,
    Object? delivery = freezed,
    Object? supplier = freezed,
    Object? receivedAt = freezed,
    Object? receivedBy = freezed,
    Object? status = freezed,
    Object? items = freezed,
    Object? comment = freezed,
    Object? checkedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? contract = freezed,
  }) {
    return _then(_$OtkDeliveryModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryId: freezed == deliveryId
          ? _value.deliveryId
          : deliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      warehouseId: freezed == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String?,
      project: freezed == project
          ? _value.project
          : project // ignore: cast_nullable_to_non_nullable
              as ProjectModel?,
      delivery: freezed == delivery
          ? _value.delivery
          : delivery // ignore: cast_nullable_to_non_nullable
              as DeliveryModel?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      receivedAt: freezed == receivedAt
          ? _value.receivedAt
          : receivedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      receivedBy: freezed == receivedBy
          ? _value.receivedBy
          : receivedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as OtkStatus?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<OtkItemModel>?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      checkedAt: freezed == checkedAt
          ? _value.checkedAt
          : checkedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contract: freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as ContractModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkDeliveryModelImpl implements _OtkDeliveryModel {
  const _$OtkDeliveryModelImpl(
      {@JsonKey(name: 'id') this.id,
      this.deliveryId,
      this.warehouseId,
      this.project,
      this.delivery,
      this.supplier,
      this.receivedAt,
      this.receivedBy,
      this.status,
      final List<OtkItemModel>? items,
      this.comment,
      this.checkedAt,
      this.createdAt,
      this.updatedAt,
      this.contract})
      : _items = items;

  factory _$OtkDeliveryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkDeliveryModelImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  final String? deliveryId;
  @override
  final String? warehouseId;
  @override
  final ProjectModel? project;
  @override
  final DeliveryModel? delivery;
  @override
  final ClientModel? supplier;
  @override
  final DateTime? receivedAt;
  @override
  final String? receivedBy;
  @override
  final OtkStatus? status;
  final List<OtkItemModel>? _items;
  @override
  List<OtkItemModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? comment;
  @override
  final DateTime? checkedAt;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
// Дополнительные поля из API
  @override
  final ContractModel? contract;

  @override
  String toString() {
    return 'OtkDeliveryModel(id: $id, deliveryId: $deliveryId, warehouseId: $warehouseId, project: $project, delivery: $delivery, supplier: $supplier, receivedAt: $receivedAt, receivedBy: $receivedBy, status: $status, items: $items, comment: $comment, checkedAt: $checkedAt, createdAt: $createdAt, updatedAt: $updatedAt, contract: $contract)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkDeliveryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.deliveryId, deliveryId) ||
                other.deliveryId == deliveryId) &&
            (identical(other.warehouseId, warehouseId) ||
                other.warehouseId == warehouseId) &&
            (identical(other.project, project) || other.project == project) &&
            (identical(other.delivery, delivery) ||
                other.delivery == delivery) &&
            (identical(other.supplier, supplier) ||
                other.supplier == supplier) &&
            (identical(other.receivedAt, receivedAt) ||
                other.receivedAt == receivedAt) &&
            (identical(other.receivedBy, receivedBy) ||
                other.receivedBy == receivedBy) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.checkedAt, checkedAt) ||
                other.checkedAt == checkedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.contract, contract) ||
                other.contract == contract));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      deliveryId,
      warehouseId,
      project,
      delivery,
      supplier,
      receivedAt,
      receivedBy,
      status,
      const DeepCollectionEquality().hash(_items),
      comment,
      checkedAt,
      createdAt,
      updatedAt,
      contract);

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkDeliveryModelImplCopyWith<_$OtkDeliveryModelImpl> get copyWith =>
      __$$OtkDeliveryModelImplCopyWithImpl<_$OtkDeliveryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkDeliveryModelImplToJson(
      this,
    );
  }
}

abstract class _OtkDeliveryModel implements OtkDeliveryModel {
  const factory _OtkDeliveryModel(
      {@JsonKey(name: 'id') final String? id,
      final String? deliveryId,
      final String? warehouseId,
      final ProjectModel? project,
      final DeliveryModel? delivery,
      final ClientModel? supplier,
      final DateTime? receivedAt,
      final String? receivedBy,
      final OtkStatus? status,
      final List<OtkItemModel>? items,
      final String? comment,
      final DateTime? checkedAt,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final ContractModel? contract}) = _$OtkDeliveryModelImpl;

  factory _OtkDeliveryModel.fromJson(Map<String, dynamic> json) =
      _$OtkDeliveryModelImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  String? get deliveryId;
  @override
  String? get warehouseId;
  @override
  ProjectModel? get project;
  @override
  DeliveryModel? get delivery;
  @override
  ClientModel? get supplier;
  @override
  DateTime? get receivedAt;
  @override
  String? get receivedBy;
  @override
  OtkStatus? get status;
  @override
  List<OtkItemModel>? get items;
  @override
  String? get comment;
  @override
  DateTime? get checkedAt;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt; // Дополнительные поля из API
  @override
  ContractModel? get contract;

  /// Create a copy of OtkDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkDeliveryModelImplCopyWith<_$OtkDeliveryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OtkProcessItem _$OtkProcessItemFromJson(Map<String, dynamic> json) {
  return _OtkProcessItem.fromJson(json);
}

/// @nodoc
mixin _$OtkProcessItem {
  String? get productId => throw _privateConstructorUsedError;
  double? get acceptedQuantity =>
      throw _privateConstructorUsedError; // double? rejectedQuantity,
  String? get defectDescription => throw _privateConstructorUsedError;
  String? get qcComment => throw _privateConstructorUsedError;

  /// Serializes this OtkProcessItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkProcessItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkProcessItemCopyWith<OtkProcessItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkProcessItemCopyWith<$Res> {
  factory $OtkProcessItemCopyWith(
          OtkProcessItem value, $Res Function(OtkProcessItem) then) =
      _$OtkProcessItemCopyWithImpl<$Res, OtkProcessItem>;
  @useResult
  $Res call(
      {String? productId,
      double? acceptedQuantity,
      String? defectDescription,
      String? qcComment});
}

/// @nodoc
class _$OtkProcessItemCopyWithImpl<$Res, $Val extends OtkProcessItem>
    implements $OtkProcessItemCopyWith<$Res> {
  _$OtkProcessItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkProcessItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? acceptedQuantity = freezed,
    Object? defectDescription = freezed,
    Object? qcComment = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      acceptedQuantity: freezed == acceptedQuantity
          ? _value.acceptedQuantity
          : acceptedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      defectDescription: freezed == defectDescription
          ? _value.defectDescription
          : defectDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      qcComment: freezed == qcComment
          ? _value.qcComment
          : qcComment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtkProcessItemImplCopyWith<$Res>
    implements $OtkProcessItemCopyWith<$Res> {
  factory _$$OtkProcessItemImplCopyWith(_$OtkProcessItemImpl value,
          $Res Function(_$OtkProcessItemImpl) then) =
      __$$OtkProcessItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      double? acceptedQuantity,
      String? defectDescription,
      String? qcComment});
}

/// @nodoc
class __$$OtkProcessItemImplCopyWithImpl<$Res>
    extends _$OtkProcessItemCopyWithImpl<$Res, _$OtkProcessItemImpl>
    implements _$$OtkProcessItemImplCopyWith<$Res> {
  __$$OtkProcessItemImplCopyWithImpl(
      _$OtkProcessItemImpl _value, $Res Function(_$OtkProcessItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkProcessItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? acceptedQuantity = freezed,
    Object? defectDescription = freezed,
    Object? qcComment = freezed,
  }) {
    return _then(_$OtkProcessItemImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      acceptedQuantity: freezed == acceptedQuantity
          ? _value.acceptedQuantity
          : acceptedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      defectDescription: freezed == defectDescription
          ? _value.defectDescription
          : defectDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      qcComment: freezed == qcComment
          ? _value.qcComment
          : qcComment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkProcessItemImpl implements _OtkProcessItem {
  const _$OtkProcessItemImpl(
      {this.productId,
      this.acceptedQuantity,
      this.defectDescription,
      this.qcComment});

  factory _$OtkProcessItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkProcessItemImplFromJson(json);

  @override
  final String? productId;
  @override
  final double? acceptedQuantity;
// double? rejectedQuantity,
  @override
  final String? defectDescription;
  @override
  final String? qcComment;

  @override
  String toString() {
    return 'OtkProcessItem(productId: $productId, acceptedQuantity: $acceptedQuantity, defectDescription: $defectDescription, qcComment: $qcComment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkProcessItemImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.acceptedQuantity, acceptedQuantity) ||
                other.acceptedQuantity == acceptedQuantity) &&
            (identical(other.defectDescription, defectDescription) ||
                other.defectDescription == defectDescription) &&
            (identical(other.qcComment, qcComment) ||
                other.qcComment == qcComment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, productId, acceptedQuantity, defectDescription, qcComment);

  /// Create a copy of OtkProcessItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkProcessItemImplCopyWith<_$OtkProcessItemImpl> get copyWith =>
      __$$OtkProcessItemImplCopyWithImpl<_$OtkProcessItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkProcessItemImplToJson(
      this,
    );
  }
}

abstract class _OtkProcessItem implements OtkProcessItem {
  const factory _OtkProcessItem(
      {final String? productId,
      final double? acceptedQuantity,
      final String? defectDescription,
      final String? qcComment}) = _$OtkProcessItemImpl;

  factory _OtkProcessItem.fromJson(Map<String, dynamic> json) =
      _$OtkProcessItemImpl.fromJson;

  @override
  String? get productId;
  @override
  double? get acceptedQuantity; // double? rejectedQuantity,
  @override
  String? get defectDescription;
  @override
  String? get qcComment;

  /// Create a copy of OtkProcessItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkProcessItemImplCopyWith<_$OtkProcessItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OtkItemModel _$OtkItemModelFromJson(Map<String, dynamic> json) {
  return _OtkItemModel.fromJson(json);
}

/// @nodoc
mixin _$OtkItemModel {
  String? get productId => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  double? get quantityApproved => throw _privateConstructorUsedError;
  double? get quantityRejected => throw _privateConstructorUsedError;
  String? get rejectionReason => throw _privateConstructorUsedError;
  bool? get isApproved =>
      throw _privateConstructorUsedError; // Дополнительные поля из API
  Map<String, dynamic>? get quantitiesInUnits =>
      throw _privateConstructorUsedError;
  List<String>? get availableUnits => throw _privateConstructorUsedError;
  String? get baseUnit => throw _privateConstructorUsedError;

  /// Serializes this OtkItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtkItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtkItemModelCopyWith<OtkItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtkItemModelCopyWith<$Res> {
  factory $OtkItemModelCopyWith(
          OtkItemModel value, $Res Function(OtkItemModel) then) =
      _$OtkItemModelCopyWithImpl<$Res, OtkItemModel>;
  @useResult
  $Res call(
      {String? productId,
      String? materialName,
      String? productName,
      double? quantity,
      double? quantityApproved,
      double? quantityRejected,
      String? rejectionReason,
      bool? isApproved,
      Map<String, dynamic>? quantitiesInUnits,
      List<String>? availableUnits,
      String? baseUnit});
}

/// @nodoc
class _$OtkItemModelCopyWithImpl<$Res, $Val extends OtkItemModel>
    implements $OtkItemModelCopyWith<$Res> {
  _$OtkItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtkItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? materialName = freezed,
    Object? productName = freezed,
    Object? quantity = freezed,
    Object? quantityApproved = freezed,
    Object? quantityRejected = freezed,
    Object? rejectionReason = freezed,
    Object? isApproved = freezed,
    Object? quantitiesInUnits = freezed,
    Object? availableUnits = freezed,
    Object? baseUnit = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityApproved: freezed == quantityApproved
          ? _value.quantityApproved
          : quantityApproved // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityRejected: freezed == quantityRejected
          ? _value.quantityRejected
          : quantityRejected // ignore: cast_nullable_to_non_nullable
              as double?,
      rejectionReason: freezed == rejectionReason
          ? _value.rejectionReason
          : rejectionReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isApproved: freezed == isApproved
          ? _value.isApproved
          : isApproved // ignore: cast_nullable_to_non_nullable
              as bool?,
      quantitiesInUnits: freezed == quantitiesInUnits
          ? _value.quantitiesInUnits
          : quantitiesInUnits // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      availableUnits: freezed == availableUnits
          ? _value.availableUnits
          : availableUnits // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      baseUnit: freezed == baseUnit
          ? _value.baseUnit
          : baseUnit // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtkItemModelImplCopyWith<$Res>
    implements $OtkItemModelCopyWith<$Res> {
  factory _$$OtkItemModelImplCopyWith(
          _$OtkItemModelImpl value, $Res Function(_$OtkItemModelImpl) then) =
      __$$OtkItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      String? materialName,
      String? productName,
      double? quantity,
      double? quantityApproved,
      double? quantityRejected,
      String? rejectionReason,
      bool? isApproved,
      Map<String, dynamic>? quantitiesInUnits,
      List<String>? availableUnits,
      String? baseUnit});
}

/// @nodoc
class __$$OtkItemModelImplCopyWithImpl<$Res>
    extends _$OtkItemModelCopyWithImpl<$Res, _$OtkItemModelImpl>
    implements _$$OtkItemModelImplCopyWith<$Res> {
  __$$OtkItemModelImplCopyWithImpl(
      _$OtkItemModelImpl _value, $Res Function(_$OtkItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtkItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? materialName = freezed,
    Object? productName = freezed,
    Object? quantity = freezed,
    Object? quantityApproved = freezed,
    Object? quantityRejected = freezed,
    Object? rejectionReason = freezed,
    Object? isApproved = freezed,
    Object? quantitiesInUnits = freezed,
    Object? availableUnits = freezed,
    Object? baseUnit = freezed,
  }) {
    return _then(_$OtkItemModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityApproved: freezed == quantityApproved
          ? _value.quantityApproved
          : quantityApproved // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityRejected: freezed == quantityRejected
          ? _value.quantityRejected
          : quantityRejected // ignore: cast_nullable_to_non_nullable
              as double?,
      rejectionReason: freezed == rejectionReason
          ? _value.rejectionReason
          : rejectionReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isApproved: freezed == isApproved
          ? _value.isApproved
          : isApproved // ignore: cast_nullable_to_non_nullable
              as bool?,
      quantitiesInUnits: freezed == quantitiesInUnits
          ? _value._quantitiesInUnits
          : quantitiesInUnits // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      availableUnits: freezed == availableUnits
          ? _value._availableUnits
          : availableUnits // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      baseUnit: freezed == baseUnit
          ? _value.baseUnit
          : baseUnit // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$OtkItemModelImpl implements _OtkItemModel {
  const _$OtkItemModelImpl(
      {this.productId,
      this.materialName,
      this.productName,
      this.quantity,
      this.quantityApproved,
      this.quantityRejected,
      this.rejectionReason,
      this.isApproved,
      final Map<String, dynamic>? quantitiesInUnits,
      final List<String>? availableUnits,
      this.baseUnit})
      : _quantitiesInUnits = quantitiesInUnits,
        _availableUnits = availableUnits;

  factory _$OtkItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtkItemModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final String? materialName;
  @override
  final String? productName;
  @override
  final double? quantity;
  @override
  final double? quantityApproved;
  @override
  final double? quantityRejected;
  @override
  final String? rejectionReason;
  @override
  final bool? isApproved;
// Дополнительные поля из API
  final Map<String, dynamic>? _quantitiesInUnits;
// Дополнительные поля из API
  @override
  Map<String, dynamic>? get quantitiesInUnits {
    final value = _quantitiesInUnits;
    if (value == null) return null;
    if (_quantitiesInUnits is EqualUnmodifiableMapView)
      return _quantitiesInUnits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _availableUnits;
  @override
  List<String>? get availableUnits {
    final value = _availableUnits;
    if (value == null) return null;
    if (_availableUnits is EqualUnmodifiableListView) return _availableUnits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? baseUnit;

  @override
  String toString() {
    return 'OtkItemModel(productId: $productId, materialName: $materialName, productName: $productName, quantity: $quantity, quantityApproved: $quantityApproved, quantityRejected: $quantityRejected, rejectionReason: $rejectionReason, isApproved: $isApproved, quantitiesInUnits: $quantitiesInUnits, availableUnits: $availableUnits, baseUnit: $baseUnit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtkItemModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.quantityApproved, quantityApproved) ||
                other.quantityApproved == quantityApproved) &&
            (identical(other.quantityRejected, quantityRejected) ||
                other.quantityRejected == quantityRejected) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason) &&
            (identical(other.isApproved, isApproved) ||
                other.isApproved == isApproved) &&
            const DeepCollectionEquality()
                .equals(other._quantitiesInUnits, _quantitiesInUnits) &&
            const DeepCollectionEquality()
                .equals(other._availableUnits, _availableUnits) &&
            (identical(other.baseUnit, baseUnit) ||
                other.baseUnit == baseUnit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      materialName,
      productName,
      quantity,
      quantityApproved,
      quantityRejected,
      rejectionReason,
      isApproved,
      const DeepCollectionEquality().hash(_quantitiesInUnits),
      const DeepCollectionEquality().hash(_availableUnits),
      baseUnit);

  /// Create a copy of OtkItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtkItemModelImplCopyWith<_$OtkItemModelImpl> get copyWith =>
      __$$OtkItemModelImplCopyWithImpl<_$OtkItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtkItemModelImplToJson(
      this,
    );
  }
}

abstract class _OtkItemModel implements OtkItemModel {
  const factory _OtkItemModel(
      {final String? productId,
      final String? materialName,
      final String? productName,
      final double? quantity,
      final double? quantityApproved,
      final double? quantityRejected,
      final String? rejectionReason,
      final bool? isApproved,
      final Map<String, dynamic>? quantitiesInUnits,
      final List<String>? availableUnits,
      final String? baseUnit}) = _$OtkItemModelImpl;

  factory _OtkItemModel.fromJson(Map<String, dynamic> json) =
      _$OtkItemModelImpl.fromJson;

  @override
  String? get productId;
  @override
  String? get materialName;
  @override
  String? get productName;
  @override
  double? get quantity;
  @override
  double? get quantityApproved;
  @override
  double? get quantityRejected;
  @override
  String? get rejectionReason;
  @override
  bool? get isApproved; // Дополнительные поля из API
  @override
  Map<String, dynamic>? get quantitiesInUnits;
  @override
  List<String>? get availableUnits;
  @override
  String? get baseUnit;

  /// Create a copy of OtkItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtkItemModelImplCopyWith<_$OtkItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
