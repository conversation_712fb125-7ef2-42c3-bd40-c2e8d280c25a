import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/shared/styles/colors.dart';

part 'delivery.freezed.dart';
part 'delivery.g.dart';

@freezed
class OtkDeliveriesResponse with _$OtkDeliveriesResponse {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDeliveriesResponse({
    @Default([]) List<OtkDeliveryModel> deliveries,
    @Default(0) int total,
  }) = _OtkDeliveriesResponse;

  factory OtkDeliveriesResponse.fromJson(Map<String, dynamic> json) =>
      _$OtkDeliveriesResponseFromJson(json);
}

@freezed
class OtkDeliveryModel with _$OtkDeliveryModel {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDeliveryModel({
    @JsonKey(name: 'id') String? id,
    String? deliveryId,
    String? warehouseId,
    ProjectModel? project,
    DeliveryModel? delivery,
    ClientModel? supplier,
    DateTime? receivedAt,
    String? receivedBy,
    OtkStatus? status,
    List<OtkItemModel>? items,
    String? comment,
    DateTime? checkedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    // Дополнительные поля из API
    ContractModel? contract,
  }) = _OtkDeliveryModel;

  factory OtkDeliveryModel.fromJson(Map<String, dynamic> json) =>
      _$OtkDeliveryModelFromJson(json);
}

@freezed
class OtkProcessItem with _$OtkProcessItem {
  @JsonSerializable(includeIfNull: false)
  const factory OtkProcessItem({
    String? productId,
    double? acceptedQuantity,
    // double? rejectedQuantity,
    String? defectDescription,
    String? qcComment,
  }) = _OtkProcessItem;

  factory OtkProcessItem.fromJson(Map<String, dynamic> json) =>
      _$OtkProcessItemFromJson(json);
}

@freezed
class OtkItemModel with _$OtkItemModel {
  const OtkItemModel._();

  @JsonSerializable(includeIfNull: false)
  const factory OtkItemModel({
    // Вложенный объект продукта
    ProductModel? product,
    // // Поля для обратной совместимости
    // String? productId,
    // String? materialName,
    // String? productName,
    // double? quantity,
    // double? quantityApproved,
    // double? quantityRejected,
    // String? rejectionReason,
    // bool? isApproved,
    // // Дополнительные поля из API
    // Map<String, dynamic>? quantitiesInUnits,
    // List<String>? availableUnits,
    // String? baseUnit,
    // // Новые поля из API
    // String? repairWorker,
    // bool? canStartRepair,
    // bool? isInProgress,
    // bool? isCompleted,
    // bool? canMakeDecision,
    // bool? needsRecheck,
    // double? repairDuration,
  }) = _OtkItemModel;

  factory OtkItemModel.fromJson(Map<String, dynamic> json) =>
      _$OtkItemModelFromJson(json);

  static OtkProcessItem toOtkProcessItem(
    OtkItemModel item, {
    double? acceptedQuantity,
  }) {
    return OtkProcessItem(
        productId: item.product?.id, acceptedQuantity: acceptedQuantity
        // acceptedQuantity ?? item.quantityApproved ?? item.quantity,
        // rejectedQuantity: item.quantityRejected ?? 0,
        // defectDescription: item.rejectionReason,
        // qcComment: item.qcComment,
        );
  }
}

enum OtkStatus {
  rejected,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('pending_qc')
  pendingQc,
  @JsonValue('qc_approved')
  qcApproved,
  @JsonValue('qc_rejected')
  qcRejected,
  @JsonValue('qc_part_rejected')
  qcPartRejected;

  String getName() {
    switch (this) {
      case OtkStatus.inProgress:
        return 'В процессе';
      case OtkStatus.pendingQc:
        return 'Ожидание контроля качества';
      case OtkStatus.qcApproved:
        return 'Контроль качества пройден';
      case OtkStatus.qcRejected:
        return 'Контроль качества не пройден';
      case OtkStatus.qcPartRejected:
        return 'Контроль качества частично не пройден';
      case OtkStatus.rejected:
        return 'Отклонено';
    }
  }

  Color getColor() {
    switch (this) {
      case OtkStatus.inProgress:
        return AppColors.lightSecondary;
      case OtkStatus.pendingQc:
        return AppColors.lightWarning;
      case OtkStatus.qcApproved:
        return AppColors.lightSuccess;
      case OtkStatus.qcRejected:
        return AppColors.lightError;
      case OtkStatus.qcPartRejected:
        return AppColors.lightError;
      case OtkStatus.rejected:
        return AppColors.lightError;
    }
  }
}
