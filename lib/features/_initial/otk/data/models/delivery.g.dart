// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OtkDeliveriesResponseImpl _$$OtkDeliveriesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$OtkDeliveriesResponseImpl(
      deliveries: (json['deliveries'] as List<dynamic>?)
              ?.map((e) => OtkDeliveryModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      total: (json['total'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$OtkDeliveriesResponseImplToJson(
        _$OtkDeliveriesResponseImpl instance) =>
    <String, dynamic>{
      'deliveries': instance.deliveries,
      'total': instance.total,
    };

_$OtkDeliveryModelImpl _$$OtkDeliveryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$OtkDeliveryModelImpl(
      id: json['id'] as String?,
      deliveryId: json['deliveryId'] as String?,
      warehouseId: json['warehouseId'] as String?,
      project: json['project'] == null
          ? null
          : ProjectModel.fromJson(json['project'] as Map<String, dynamic>),
      delivery: json['delivery'] == null
          ? null
          : DeliveryModel.fromJson(json['delivery'] as Map<String, dynamic>),
      supplier: json['supplier'] == null
          ? null
          : ClientModel.fromJson(json['supplier'] as Map<String, dynamic>),
      receivedAt: json['receivedAt'] == null
          ? null
          : DateTime.parse(json['receivedAt'] as String),
      receivedBy: json['receivedBy'] as String?,
      status: $enumDecodeNullable(_$OtkStatusEnumMap, json['status']),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => OtkItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      comment: json['comment'] as String?,
      checkedAt: json['checkedAt'] == null
          ? null
          : DateTime.parse(json['checkedAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      contract: json['contract'] == null
          ? null
          : ContractModel.fromJson(json['contract'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OtkDeliveryModelImplToJson(
        _$OtkDeliveryModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.deliveryId case final value?) 'deliveryId': value,
      if (instance.warehouseId case final value?) 'warehouseId': value,
      if (instance.project case final value?) 'project': value,
      if (instance.delivery case final value?) 'delivery': value,
      if (instance.supplier case final value?) 'supplier': value,
      if (instance.receivedAt?.toIso8601String() case final value?)
        'receivedAt': value,
      if (instance.receivedBy case final value?) 'receivedBy': value,
      if (_$OtkStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.items case final value?) 'items': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.checkedAt?.toIso8601String() case final value?)
        'checkedAt': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.contract case final value?) 'contract': value,
    };

const _$OtkStatusEnumMap = {
  OtkStatus.rejected: 'rejected',
  OtkStatus.inProgress: 'in_progress',
  OtkStatus.pendingQc: 'pending_qc',
  OtkStatus.qcApproved: 'qc_approved',
  OtkStatus.qcRejected: 'qc_rejected',
  OtkStatus.qcPartRejected: 'qc_part_rejected',
};

_$OtkProcessItemImpl _$$OtkProcessItemImplFromJson(Map<String, dynamic> json) =>
    _$OtkProcessItemImpl(
      productId: json['productId'] as String?,
      acceptedQuantity: (json['acceptedQuantity'] as num?)?.toDouble(),
      defectDescription: json['defectDescription'] as String?,
      qcComment: json['qcComment'] as String?,
    );

Map<String, dynamic> _$$OtkProcessItemImplToJson(
        _$OtkProcessItemImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.acceptedQuantity case final value?)
        'acceptedQuantity': value,
      if (instance.defectDescription case final value?)
        'defectDescription': value,
      if (instance.qcComment case final value?) 'qcComment': value,
    };

_$OtkItemModelImpl _$$OtkItemModelImplFromJson(Map<String, dynamic> json) =>
    _$OtkItemModelImpl(
      productId: json['productId'] as String?,
      materialName: json['materialName'] as String?,
      productName: json['productName'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
      quantityApproved: (json['quantityApproved'] as num?)?.toDouble(),
      quantityRejected: (json['quantityRejected'] as num?)?.toDouble(),
      rejectionReason: json['rejectionReason'] as String?,
      isApproved: json['isApproved'] as bool?,
      quantitiesInUnits: json['quantitiesInUnits'] as Map<String, dynamic>?,
      availableUnits: (json['availableUnits'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      baseUnit: json['baseUnit'] as String?,
    );

Map<String, dynamic> _$$OtkItemModelImplToJson(_$OtkItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.materialName case final value?) 'materialName': value,
      if (instance.productName case final value?) 'productName': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.quantityApproved case final value?)
        'quantityApproved': value,
      if (instance.quantityRejected case final value?)
        'quantityRejected': value,
      if (instance.rejectionReason case final value?) 'rejectionReason': value,
      if (instance.isApproved case final value?) 'isApproved': value,
      if (instance.quantitiesInUnits case final value?)
        'quantitiesInUnits': value,
      if (instance.availableUnits case final value?) 'availableUnits': value,
      if (instance.baseUnit case final value?) 'baseUnit': value,
    };
